/**
 * RelayMill Theme Animations
 * 
 * @package RelayMill
 * @version 1.0
 */

(function($) {
    'use strict';

    // Animation CSS to be injected
    var animationCSS = `
        <style id="relaymill-animations">
            /* Animation keyframes */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes fadeInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes fadeInRight {
                from {
                    opacity: 0;
                    transform: translateX(30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes scaleIn {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes pulse {
                0% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.05);
                }
                100% {
                    transform: scale(1);
                }
            }

            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% {
                    transform: translate3d(0,0,0);
                }
                40%, 43% {
                    transform: translate3d(0, -10px, 0);
                }
                70% {
                    transform: translate3d(0, -5px, 0);
                }
                90% {
                    transform: translate3d(0, -2px, 0);
                }
            }

            /* Animation classes */
            .animate-fade-in-up {
                animation: fadeInUp 0.8s ease-out forwards;
            }

            .animate-fade-in-left {
                animation: fadeInLeft 0.8s ease-out forwards;
            }

            .animate-fade-in-right {
                animation: fadeInRight 0.8s ease-out forwards;
            }

            .animate-scale-in {
                animation: scaleIn 0.6s ease-out forwards;
            }

            .animate-slide-in-down {
                animation: slideInDown 0.8s ease-out forwards;
            }

            .animate-pulse {
                animation: pulse 2s infinite;
            }

            .animate-bounce {
                animation: bounce 1s;
            }

            /* Scroll animations */
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.8s ease-out;
            }

            .animate-on-scroll.animated {
                opacity: 1;
                transform: translateY(0);
            }

            /* Staggered animations */
            .animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
            .animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
            .animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
            .animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }
            .animate-on-scroll:nth-child(5) { transition-delay: 0.5s; }
            .animate-on-scroll:nth-child(6) { transition-delay: 0.6s; }

            /* Hover animations */
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }

            .hover-scale {
                transition: transform 0.3s ease;
            }

            .hover-scale:hover {
                transform: scale(1.05);
            }

            /* Loading animations */
            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid rgba(255,255,255,.3);
                border-radius: 50%;
                border-top-color: #fff;
                animation: spin 1s ease-in-out infinite;
            }

            @keyframes spin {
                to { transform: rotate(360deg); }
            }

            /* Notification animations */
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transform: translateX(400px);
                transition: transform 0.3s ease;
                max-width: 350px;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification-success {
                background: linear-gradient(45deg, #28a745, #20c997);
            }

            .notification-error {
                background: linear-gradient(45deg, #dc3545, #fd7e14);
            }

            .notification-info {
                background: linear-gradient(45deg, #007bff, #6f42c1);
            }

            /* Form field error animation */
            .form-group input.error,
            .form-group select.error,
            .form-group textarea.error {
                border-color: #dc3545;
                animation: shake 0.5s ease-in-out;
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }

            /* Hero section animations */
            .hero-content {
                opacity: 0;
                transform: translateY(50px);
            }

            .hero-content.animate-fade-in-up {
                opacity: 1;
                transform: translateY(0);
            }

            /* Product card animations */
            .product-card {
                transition: all 0.3s ease;
            }

            .product-card.hovered {
                transform: translateY(-10px) scale(1.02);
            }

            /* Button animations */
            .btn {
                position: relative;
                overflow: hidden;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            .btn:hover::before {
                left: 100%;
            }

            /* Scroll indicator */
            .scroll-indicator {
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 3px;
                background: linear-gradient(45deg, #667eea, #764ba2);
                z-index: 9999;
                transition: width 0.1s ease;
            }

            /* Mobile menu animations */
            .mobile-menu-overlay {
                backdrop-filter: blur(5px);
            }

            .mobile-nav-menu li {
                opacity: 0;
                transform: translateX(50px);
                transition: all 0.3s ease;
            }

            .mobile-menu-overlay.active .mobile-nav-menu li {
                opacity: 1;
                transform: translateX(0);
            }

            .mobile-nav-menu li:nth-child(1) { transition-delay: 0.1s; }
            .mobile-nav-menu li:nth-child(2) { transition-delay: 0.2s; }
            .mobile-nav-menu li:nth-child(3) { transition-delay: 0.3s; }
            .mobile-nav-menu li:nth-child(4) { transition-delay: 0.4s; }
            .mobile-nav-menu li:nth-child(5) { transition-delay: 0.5s; }

            /* Reduce motion for accessibility */
            @media (prefers-reduced-motion: reduce) {
                *,
                *::before,
                *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        </style>
    `;

    // Inject animation CSS
    $('head').append(animationCSS);

    // Document ready
    $(document).ready(function() {
        initScrollIndicator();
        initAdvancedAnimations();
    });

    /**
     * Initialize scroll indicator
     */
    function initScrollIndicator() {
        // Create scroll indicator
        $('body').prepend('<div class="scroll-indicator"></div>');
        
        $(window).on('scroll', function() {
            var scrollTop = $(window).scrollTop();
            var docHeight = $(document).height() - $(window).height();
            var scrollPercent = (scrollTop / docHeight) * 100;
            
            $('.scroll-indicator').css('width', scrollPercent + '%');
        });
    }

    /**
     * Initialize advanced animations
     */
    function initAdvancedAnimations() {
        // Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            var animationObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');
                        animationObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe elements
            document.querySelectorAll('.animate-on-scroll').forEach(function(el) {
                animationObserver.observe(el);
            });
        }

        // Parallax effect for hero background
        $(window).on('scroll', function() {
            var scrolled = $(window).scrollTop();
            var parallax = $('.hero-section');
            var speed = 0.5;
            
            if (parallax.length) {
                var yPos = -(scrolled * speed);
                parallax.css('background-position', 'center ' + yPos + 'px');
            }
        });

        // Add hover effects to interactive elements
        $('.product-card, .contact-item, .btn').addClass('hover-lift');
        
        // Typing effect for hero title (optional)
        if ($('.hero-title').length && $('.hero-title').data('typing')) {
            typeWriter('.hero-title', $('.hero-title').text(), 100);
        }
    }

    /**
     * Typewriter effect
     */
    function typeWriter(element, text, speed) {
        var i = 0;
        var elem = $(element);
        elem.text('');
        
        function type() {
            if (i < text.length) {
                elem.text(elem.text() + text.charAt(i));
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    /**
     * Animate counter numbers
     */
    function animateCounters() {
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');
            
            $({ countNum: $this.text() }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    }

    // Export functions for global use
    window.RelayMillAnimations = {
        animateCounters: animateCounters,
        typeWriter: typeWriter
    };

})(jQuery);
