/**
 * RelayMill Theme Main JavaScript
 * 
 * @package RelayMill
 * @version 1.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initializeTheme();
    });

    // Window load
    $(window).on('load', function() {
        handlePageLoad();
    });

    // Window scroll
    $(window).on('scroll', function() {
        handleScroll();
    });

    // Window resize
    $(window).on('resize', function() {
        handleResize();
    });

    /**
     * Initialize theme functionality
     */
    function initializeTheme() {
        initMobileMenu();
        initSmoothScrolling();
        initContactForm();
        initBackToTop();
        initAnimations();
        initProductCards();
    }

    /**
     * Handle page load events
     */
    function handlePageLoad() {
        // Hide loading spinner if exists
        $('.loading-spinner').fadeOut();
        
        // Trigger entrance animations
        triggerEntranceAnimations();
    }

    /**
     * Handle scroll events
     */
    function handleScroll() {
        var scrollTop = $(window).scrollTop();
        
        // Back to top button visibility
        if (scrollTop > 300) {
            $('.back-to-top').addClass('visible');
        } else {
            $('.back-to-top').removeClass('visible');
        }
        
        // Header scroll effect
        if (scrollTop > 100) {
            $('.site-header').addClass('scrolled');
        } else {
            $('.site-header').removeClass('scrolled');
        }
        
        // Parallax effect for hero section
        if ($('.hero-section').length) {
            var parallaxSpeed = 0.5;
            $('.hero-section').css('transform', 'translateY(' + (scrollTop * parallaxSpeed) + 'px)');
        }
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        // Close mobile menu on resize to desktop
        if ($(window).width() > 768) {
            $('.mobile-menu-overlay').removeClass('active');
            $('body').removeClass('menu-open');
        }
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        // Mobile menu toggle
        $('.mobile-menu-toggle').on('click', function() {
            $('.mobile-menu-overlay').addClass('active');
            $('body').addClass('menu-open');
        });

        // Close mobile menu
        $('.mobile-menu-close, .mobile-menu-overlay').on('click', function(e) {
            if (e.target === this) {
                $('.mobile-menu-overlay').removeClass('active');
                $('body').removeClass('menu-open');
            }
        });

        // Close menu when clicking on links
        $('.mobile-nav-menu a').on('click', function() {
            $('.mobile-menu-overlay').removeClass('active');
            $('body').removeClass('menu-open');
        });

        // Prevent menu content clicks from closing menu
        $('.mobile-menu-content').on('click', function(e) {
            e.stopPropagation();
        });
    }

    /**
     * Initialize smooth scrolling
     */
    function initSmoothScrolling() {
        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.getAttribute('href'));
            
            if (target.length) {
                e.preventDefault();
                var headerHeight = $('.site-header').outerHeight();
                var targetOffset = target.offset().top - headerHeight - 20;
                
                $('html, body').animate({
                    scrollTop: targetOffset
                }, 800, 'easeInOutQuart');
            }
        });

        // Back to top functionality
        $('.back-to-top').on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 800, 'easeInOutQuart');
        });
    }

    /**
     * Initialize contact form
     */
    function initContactForm() {
        $('#contact-form').on('submit', function(e) {
            e.preventDefault();
            
            var form = $(this);
            var formData = new FormData(this);
            var submitBtn = form.find('button[type="submit"]');
            var originalText = submitBtn.html();
            
            // Validate form
            if (!validateContactForm(form)) {
                return false;
            }
            
            // Show loading state
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Sending...');
            submitBtn.prop('disabled', true);
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(function() {
                // Show success message
                showNotification('Message sent successfully! We will get back to you soon.', 'success');
                
                // Reset form
                form[0].reset();
                
                // Reset button
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }, 2000);
        });
    }

    /**
     * Validate contact form
     */
    function validateContactForm(form) {
        var isValid = true;
        var requiredFields = form.find('[required]');
        
        requiredFields.each(function() {
            var field = $(this);
            var value = field.val().trim();
            
            // Remove previous error styling
            field.removeClass('error');
            
            if (!value) {
                field.addClass('error');
                isValid = false;
            }
            
            // Email validation
            if (field.attr('type') === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    field.addClass('error');
                    isValid = false;
                }
            }
        });
        
        if (!isValid) {
            showNotification('Please fill in all required fields correctly.', 'error');
        }
        
        return isValid;
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        var notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
        
        $('body').append(notification);
        
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 4000);
    }

    /**
     * Initialize back to top button
     */
    function initBackToTop() {
        // Already handled in scroll function
    }

    /**
     * Initialize animations
     */
    function initAnimations() {
        // Add animation classes to elements
        $('.product-card').addClass('animate-on-scroll');
        $('.contact-item').addClass('animate-on-scroll');
        $('.section-header').addClass('animate-on-scroll');
        
        // Trigger animations on scroll
        $(window).on('scroll', function() {
            $('.animate-on-scroll').each(function() {
                var element = $(this);
                var elementTop = element.offset().top;
                var windowBottom = $(window).scrollTop() + $(window).height();
                
                if (elementTop < windowBottom - 100) {
                    element.addClass('animated');
                }
            });
        });
    }

    /**
     * Initialize product cards
     */
    function initProductCards() {
        $('.product-card').on('mouseenter', function() {
            $(this).addClass('hovered');
        }).on('mouseleave', function() {
            $(this).removeClass('hovered');
        });
        
        // Product card click handling
        $('.product-card').on('click', function(e) {
            if (!$(e.target).hasClass('product-cta')) {
                var ctaLink = $(this).find('.product-cta');
                if (ctaLink.length) {
                    window.location.href = ctaLink.attr('href');
                }
            }
        });
    }

    /**
     * Trigger entrance animations
     */
    function triggerEntranceAnimations() {
        $('.hero-content').addClass('animate-fade-in-up');
        
        setTimeout(function() {
            $('.product-section').first().find('.section-header').addClass('animate-fade-in-up');
        }, 500);
    }

    /**
     * Custom easing function
     */
    $.easing.easeInOutQuart = function(x, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t * t + b;
        return -c / 2 * ((t -= 2) * t * t * t - 2) + b;
    };

    /**
     * Lazy loading for images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    // Initialize lazy loading
    initLazyLoading();

})(jQuery);
