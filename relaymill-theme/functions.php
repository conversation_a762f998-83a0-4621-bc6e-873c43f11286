<?php
/**
 * RelayMill Theme Functions
 * 
 * @package RelayMill
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function relaymill_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'relaymill'),
        'footer' => __('Footer Menu', 'relaymill'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'relaymill_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function relaymill_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('relaymill-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue custom JavaScript
    wp_enqueue_script('relaymill-script', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Enqueue smooth scroll and animations
    wp_enqueue_script('relaymill-animations', get_template_directory_uri() . '/assets/js/animations.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('relaymill-script', 'relaymill_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('relaymill_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'relaymill_scripts');

/**
 * Register Widget Areas
 */
function relaymill_widgets_init() {
    register_sidebar(array(
        'name'          => __('Footer Widget Area 1', 'relaymill'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in the first footer column.', 'relaymill'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 2', 'relaymill'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in the second footer column.', 'relaymill'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 3', 'relaymill'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in the third footer column.', 'relaymill'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'relaymill_widgets_init');

/**
 * Custom Post Type for Products
 */
function relaymill_register_product_post_type() {
    $labels = array(
        'name'                  => _x('Products', 'Post type general name', 'relaymill'),
        'singular_name'         => _x('Product', 'Post type singular name', 'relaymill'),
        'menu_name'             => _x('Products', 'Admin Menu text', 'relaymill'),
        'name_admin_bar'        => _x('Product', 'Add New on Toolbar', 'relaymill'),
        'add_new'               => __('Add New', 'relaymill'),
        'add_new_item'          => __('Add New Product', 'relaymill'),
        'new_item'              => __('New Product', 'relaymill'),
        'edit_item'             => __('Edit Product', 'relaymill'),
        'view_item'             => __('View Product', 'relaymill'),
        'all_items'             => __('All Products', 'relaymill'),
        'search_items'          => __('Search Products', 'relaymill'),
        'parent_item_colon'     => __('Parent Products:', 'relaymill'),
        'not_found'             => __('No products found.', 'relaymill'),
        'not_found_in_trash'    => __('No products found in Trash.', 'relaymill'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'products'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => null,
        'menu_icon'          => 'dashicons-products',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'show_in_rest'       => true,
    );

    register_post_type('product', $args);
}
add_action('init', 'relaymill_register_product_post_type');

/**
 * Custom Taxonomy for Product Categories
 */
function relaymill_register_product_taxonomy() {
    $labels = array(
        'name'              => _x('Product Categories', 'taxonomy general name', 'relaymill'),
        'singular_name'     => _x('Product Category', 'taxonomy singular name', 'relaymill'),
        'search_items'      => __('Search Product Categories', 'relaymill'),
        'all_items'         => __('All Product Categories', 'relaymill'),
        'parent_item'       => __('Parent Product Category', 'relaymill'),
        'parent_item_colon' => __('Parent Product Category:', 'relaymill'),
        'edit_item'         => __('Edit Product Category', 'relaymill'),
        'update_item'       => __('Update Product Category', 'relaymill'),
        'add_new_item'      => __('Add New Product Category', 'relaymill'),
        'new_item_name'     => __('New Product Category Name', 'relaymill'),
        'menu_name'         => __('Product Categories', 'relaymill'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'product-category'),
        'show_in_rest'      => true,
    );

    register_taxonomy('product_category', array('product'), $args);
}
add_action('init', 'relaymill_register_product_taxonomy');

/**
 * Add Custom Meta Boxes for Products
 */
function relaymill_add_product_meta_boxes() {
    add_meta_box(
        'product_details',
        __('Product Details', 'relaymill'),
        'relaymill_product_details_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'relaymill_add_product_meta_boxes');

/**
 * Product Details Meta Box Callback
 */
function relaymill_product_details_callback($post) {
    wp_nonce_field('relaymill_save_product_details', 'relaymill_product_details_nonce');
    
    $specifications = get_post_meta($post->ID, '_product_specifications', true);
    $features = get_post_meta($post->ID, '_product_features', true);
    $applications = get_post_meta($post->ID, '_product_applications', true);
    
    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th><label for="product_specifications">' . __('Specifications', 'relaymill') . '</label></th>';
    echo '<td><textarea id="product_specifications" name="product_specifications" rows="4" cols="50">' . esc_textarea($specifications) . '</textarea></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="product_features">' . __('Features', 'relaymill') . '</label></th>';
    echo '<td><textarea id="product_features" name="product_features" rows="4" cols="50">' . esc_textarea($features) . '</textarea></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="product_applications">' . __('Applications', 'relaymill') . '</label></th>';
    echo '<td><textarea id="product_applications" name="product_applications" rows="4" cols="50">' . esc_textarea($applications) . '</textarea></td>';
    echo '</tr>';
    echo '</table>';
}

/**
 * Save Product Meta Data
 */
function relaymill_save_product_details($post_id) {
    if (!isset($_POST['relaymill_product_details_nonce']) || 
        !wp_verify_nonce($_POST['relaymill_product_details_nonce'], 'relaymill_save_product_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST['product_specifications'])) {
        update_post_meta($post_id, '_product_specifications', sanitize_textarea_field($_POST['product_specifications']));
    }

    if (isset($_POST['product_features'])) {
        update_post_meta($post_id, '_product_features', sanitize_textarea_field($_POST['product_features']));
    }

    if (isset($_POST['product_applications'])) {
        update_post_meta($post_id, '_product_applications', sanitize_textarea_field($_POST['product_applications']));
    }
}
add_action('save_post', 'relaymill_save_product_details');

/**
 * Customize Theme Options
 */
function relaymill_customize_register($wp_customize) {
    // Add Company Information Section
    $wp_customize->add_section('relaymill_company_info', array(
        'title'    => __('Company Information', 'relaymill'),
        'priority' => 30,
    ));

    // Phone Number
    $wp_customize->add_setting('relaymill_phone', array(
        'default'           => '+86 13559471238',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('relaymill_phone', array(
        'label'   => __('Phone Number', 'relaymill'),
        'section' => 'relaymill_company_info',
        'type'    => 'text',
    ));

    // Email Address
    $wp_customize->add_setting('relaymill_email', array(
        'default'           => '<EMAIL>',
        'sanitize_callback' => 'sanitize_email',
    ));

    $wp_customize->add_control('relaymill_email', array(
        'label'   => __('Email Address', 'relaymill'),
        'section' => 'relaymill_company_info',
        'type'    => 'email',
    ));

    // Company Address
    $wp_customize->add_setting('relaymill_address', array(
        'default'           => 'Xiamen Chengfeng Technology Co.LTD',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));

    $wp_customize->add_control('relaymill_address', array(
        'label'   => __('Company Address', 'relaymill'),
        'section' => 'relaymill_company_info',
        'type'    => 'textarea',
    ));
}
add_action('customize_register', 'relaymill_customize_register');

/**
 * Add Custom Image Sizes
 */
function relaymill_custom_image_sizes() {
    add_image_size('product-thumbnail', 400, 300, true);
    add_image_size('product-large', 800, 600, true);
    add_image_size('hero-image', 1920, 1080, true);
}
add_action('after_setup_theme', 'relaymill_custom_image_sizes');

/**
 * Optimize Performance
 */
function relaymill_optimize_performance() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'relaymill_optimize_performance');

/**
 * Security Enhancements
 */
function relaymill_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
    }
}
add_action('send_headers', 'relaymill_security_headers');
?>
