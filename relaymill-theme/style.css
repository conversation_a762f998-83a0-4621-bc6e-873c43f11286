/*
Theme Name: RelayMill Professional
Description: A modern, professional WordPress theme for RelayMill - Leading Global Relay Manufacturer. Features responsive design, product showcases, and industrial aesthetics.
Version: 1.0
Author: Custom Development
Text Domain: relaymill
*/

/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: #666;
}

a {
    color: #0066cc;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #004499;
}

/* ===== LAYOUT CONTAINERS ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 20px;
}

/* ===== HEADER STYLES ===== */
.site-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.header-top {
    background: rgba(0,0,0,0.1);
    padding: 10px 0;
    font-size: 0.9rem;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 20px;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    color: white;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
    color: #ffd700;
}

.header-main {
    padding: 15px 0;
}

.header-main .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-logo {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.site-logo:hover {
    color: #ffd700;
}

/* ===== NAVIGATION ===== */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
}

.nav-menu li a {
    color: white;
    font-weight: 500;
    padding: 10px 0;
    position: relative;
    transition: all 0.3s ease;
}

.nav-menu li a:hover {
    color: #ffd700;
}

.nav-menu li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #ffd700;
    transition: width 0.3s ease;
}

.nav-menu li a:hover::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* ===== HERO SECTION ===== */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
}

.hero-title {
    font-size: 3.8rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1.1;
}

.hero-title .highlight {
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 40px;
    opacity: 0.95;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
    font-weight: 300;
}

.hero-cta {
    display: inline-flex;
    gap: 25px;
    margin-top: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 60px;
    flex-wrap: wrap;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    font-weight: 500;
}

.hero-feature i {
    color: #ffd700;
    font-size: 1.2rem;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
    color: white;
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #333;
    transform: translateY(-2px);
}

/* ===== PRODUCT SECTIONS ===== */
.product-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.product-section:nth-child(even) {
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    color: #1a1a1a;
    margin-bottom: 15px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

/* ===== PRODUCT CARDS ===== */
.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #eee;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-content {
    padding: 25px;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1a1a1a;
    line-height: 1.3;
}

.product-description {
    color: #666;
    font-size: 0.95rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

.product-cta {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.product-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

/* ===== PRODUCT SPECIFICATIONS ===== */
.product-specs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.spec-item {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: #f8f9fa;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    color: #666;
    border: 1px solid #e9ecef;
}

.spec-item i {
    color: #667eea;
    font-size: 0.8rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-top {
        display: none;
    }
    
    .main-navigation .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .product-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    h1 { font-size: 2.2rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.7rem; }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .btn {
        padding: 12px 25px;
        font-size: 0.9rem;
    }
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-top: 40px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.2rem;
    margin-bottom: 8px;
    color: #1a1a1a;
}

.contact-details p {
    margin: 0;
    color: #666;
}

.contact-details a {
    color: #667eea;
    font-weight: 500;
}

.contact-details a:hover {
    color: #764ba2;
}

/* ===== CONTACT FORM ===== */
.contact-form {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.quote-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== FOOTER STYLES ===== */
.site-footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
}

.footer-main {
    padding: 60px 0 40px;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 40px;
}

.footer-logo {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #ffd700;
}

.footer-description {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 25px;
}

.footer-social {
    display: flex;
    gap: 15px;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: #ffd700;
    color: #1a1a1a;
    transform: translateY(-3px);
}

.footer-title {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #ffd700;
}

.footer-menu {
    list-style: none;
    padding: 0;
}

.footer-menu li {
    margin-bottom: 10px;
}

.footer-menu a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-menu a:hover {
    color: #ffd700;
}

.footer-contact {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.footer-contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ccc;
}

.footer-contact-item i {
    color: #ffd700;
    width: 20px;
}

.footer-contact-item a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-contact-item a:hover {
    color: #ffd700;
}

.footer-bottom {
    background: rgba(0,0,0,0.3);
    padding: 20px 0;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ccc;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: #ccc;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ffd700;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* ===== MOBILE MENU ===== */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background: white;
    padding: 30px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.mobile-menu-overlay.active .mobile-menu-content {
    transform: translateX(0);
}

.mobile-menu-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #333;
    cursor: pointer;
}

.mobile-nav-menu {
    list-style: none;
    padding: 0;
    margin-top: 60px;
}

.mobile-nav-menu li {
    margin-bottom: 20px;
}

.mobile-nav-menu a {
    color: #333;
    font-size: 1.1rem;
    font-weight: 500;
    text-decoration: none;
    display: block;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.mobile-contact {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.mobile-contact p {
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9rem;
}

.mobile-contact i {
    color: #667eea;
    margin-right: 10px;
}

/* ===== RESPONSIVE UPDATES ===== */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }
}
